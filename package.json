{"name": "ant-design-pro", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "repository": "**************:ant-design/ant-design-pro.git", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run biome:lint && npm run tsc", "lint-staged": "lint-staged", "biome:lint": "npx @biomejs/biome lint", "openapi": "max openapi", "prepare": "husky", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development UMI_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env UMI_ENV=dev MOCK=none max dev", "start:no-mock": "cross-env MOCK=none max dev", "start:pre": "cross-env UMI_ENV=pre MOCK=none max dev", "start:test": "cross-env UMI_ENV=test MOCK=none max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "browserslist": ["defaults"], "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.8.9", "@ant-design/v5-patch-for-react-19": "^1.0.3", "antd": "^5.26.4", "antd-style": "^3.7.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "react": "^19.1.0", "react-dom": "^19.1.0", "swagger-ui-dist": "^5.27.1"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@biomejs/biome": "^2.1.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-helmet": "^6.1.11", "@umijs/max": "^4.3.24", "cross-env": "^7.0.3", "express": "^4.21.1", "gh-pages": "^6.1.1", "husky": "^9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^16.1.2", "mockjs": "^1.1.0", "ts-node": "^10.9.2", "typescript": "^5.6.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=20.0.0"}, "create-umi": {"ignoreScript": ["docker*", "functions*", "site", "generateMock"], "ignoreDependencies": ["netlify*", "serverless"], "ignore": [".dockerignore", ".git", ".github", ".gitpod.yml", "CODE_OF_CONDUCT.md", "Dockerfile", "Dockerfile.*", "lambda", "LICENSE", "netlify.toml", "README.*.md", "azure-pipelines.yml", "docker", "CNAME", "create-umi"]}}