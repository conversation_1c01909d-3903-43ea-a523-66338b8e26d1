name: Preview Deploy

on:
  workflow_run:
    workflows: ['Preview Build']
    types:
      - completed

permissions:
  contents: read

jobs:
  success:
    permissions:
      actions: read # for dawidd6/action-download-artifact to query and download artifacts
      issues: write # for actions-cool/maintain-one-comment to modify or create issue comments
      pull-requests: write # for actions-cool/maintain-one-comment to modify or create PR comments
    runs-on: ubuntu-latest
    if: github.event.workflow_run.event == 'pull_request' && github.event.workflow_run.conclusion == 'success'
    steps:
      - name: download pr artifact
        uses: dawidd6/action-download-artifact@v6
        with:
          workflow: ${{ github.event.workflow_run.workflow_id }}
          name: pr

      - name: save PR id
        id: pr
        run: echo "::set-output name=id::$(<pr-id.txt)"

      - name: download dist artifact
        uses: dawidd6/action-download-artifact@v6
        with:
          workflow: ${{ github.event.workflow_run.workflow_id }}
          workflow_conclusion: success
          name: dist

      - name: upload surge service
        id: deploy
        run: |
          export DEPLOY_DOMAIN=https://ant-design-pro-preview-pr-${{ steps.pr.outputs.id }}.surge.sh
          npx surge --project ./ --domain $DEPLOY_DOMAIN --token ${{ secrets.SURGE_TOKEN }}

      - name: update status comment
        uses: actions-cool/maintain-one-comment@v1.2.1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          body: |
            🎊 PR Preview has been successfully built and deployed to https://ant-design-pro-preview-pr-${{ steps.pr.outputs.id }}.surge.sh

            <img width="300" src="https://user-images.githubusercontent.com/507615/90250366-88233900-de6e-11ea-95a5-84f0762ffd39.png">

            <!-- Sticky Pull Request Comment -->
          body-include: '<!-- Sticky Pull Request Comment -->'
          number: ${{ steps.pr.outputs.id }}

      - name: The job failed
        if: ${{ failure() }}
        uses: actions-cool/maintain-one-comment@v1.2.1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          body: |
            😭 Deploy PR Preview failed.

            <img width="300" src="https://user-images.githubusercontent.com/507615/90250824-4e066700-de6f-11ea-8230-600ecc3d6a6b.png">

            <!-- Sticky Pull Request Comment -->
          body-include: '<!-- Sticky Pull Request Comment -->'
          number: ${{ steps.pr.outputs.id }}

  failed:
    permissions:
      actions: read # for dawidd6/action-download-artifact to query and download artifacts
      issues: write # for actions-cool/maintain-one-comment to modify or create issue comments
      pull-requests: write # for actions-cool/maintain-one-comment to modify or create PR comments
    runs-on: ubuntu-latest
    if: github.event.workflow_run.event == 'pull_request' && github.event.workflow_run.conclusion == 'failure'
    steps:
      - name: download pr artifact
        uses: dawidd6/action-download-artifact@v6
        with:
          workflow: ${{ github.event.workflow_run.workflow_id }}
          name: pr

      - name: save PR id
        id: pr
        run: echo "::set-output name=id::$(<pr-id.txt)"

      - name: The job failed
        uses: actions-cool/maintain-one-comment@v1.2.1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          body: |
            😭 Deploy PR Preview failed.

            <img width="300" src="https://user-images.githubusercontent.com/507615/90250824-4e066700-de6f-11ea-8230-600ecc3d6a6b.png">

            <!-- Sticky Pull Request Comment -->
          body-include: '<!-- Sticky Pull Request Comment -->'
          number: ${{ steps.pr.outputs.id }}
