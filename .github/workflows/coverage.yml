name: coverage CI

on: [push, pull_request]

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: echo ${{github.ref}}
      - uses: oven-sh/setup-bun@v2
      - run: bun install
      - run: bun run test:coverage
      - uses: codecov/codecov-action@v5
