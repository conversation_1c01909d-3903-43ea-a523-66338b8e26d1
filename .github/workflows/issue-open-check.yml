name: Issue Open Check

on:
  issues:
    types: [opened, edited]

jobs:
  check-issue:
    runs-on: ubuntu-latest
    steps:
      - uses: actions-cool/issues-helper@v2.2.0
        id: check
        with:
          actions: 'check-issue'
          issue-number: ${{ github.event.issue.number }}
          title-excludes: '🐛 [BUG], 👑 [需求 | Feature], 🧐[问题 | question]'

      - if: steps.check.outputs.check-result == 'false' && github.event.issue.state == 'open'
        uses: actions-cool/issues-helper@v2.2.0
        with:
          actions: 'create-comment, close-issue'
          issue-number: ${{ github.event.issue.number }}
          body: |
            当前 Issue 未检测到标题，请规范填写，谢谢！

            The title of the current issue is not detected, please fill in according to the specifications, thank you!

      - if: steps.check.outputs.check-result == 'true'
        uses: actions-cool/issues-similarity-analysis@v1
        with:
          filter-threshold: 0.8
          title-excludes: '🐛[BUG], 👑 [需求 | Feature], 🧐[问题 | question]'
          comment-title: '### 以下的 Issues 可能会帮助到你 / The following issues may help you'
          show-footer: false
